
/**
 * 缓存初始化器
 * 负责在应用启动时初始化缓存系统
 */
export class CacheInitializer {

  private static initialized = false

  /**
   * 初始化缓存系统
   */
  public static async initialize(): Promise<void> {
    if (CacheInitializer.initialized) {
      return
    }

    try {
      console.log('[缓存系统] 开始初始化...')

      CacheInitializer.initialized = true
      console.log('[缓存系统] 初始化完成')
    } catch (error) {
      console.error('[缓存系统] 初始化失败:', error)
      throw error
    }
  }

  /**
   * 检查缓存系统是否已初始化
   */
  public static isInitialized(): boolean {
    return CacheInitializer.initialized
  }

  /**
   * 重置初始化状态（主要用于测试）
   */
  public static reset(): void {
    CacheInitializer.initialized = false
  }
}

/**
 * 自动初始化缓存系统
 * 在模块加载时自动调用
 */
CacheInitializer.initialize().catch(error => {
  console.error('[缓存系统] 自动初始化失败:', error)
})
