import { Injectable } from '@nestjs/common'
import fetch from 'node-fetch'
import { ApiResponse } from '@app/shared/types/database.types.js'

/**
 * 请求配置接口
 */
export interface RequestConfig {
  headers?: Record<string, string>
  timeout?: number
}

/**
 * 业务错误类
 */
export class BusinessError extends Error {

  constructor(
    public readonly code: number,
    message: string,
    public readonly originalData?: any
  ) {
    super(message)
    this.name = 'BusinessError'
  }
}

/**
 * 统一请求服务
 * 封装通用的 HTTP 请求逻辑
 */
@Injectable()
export class RequestService {

  /**
   * 基础URL配置
   */
  private readonly baseUrl: string = process.env.API_BASE_URL || 'http://47.99.131.55:48080'

  /**
   * 默认请求头
   */
  private readonly defaultHeaders = {
    'Content-Type': 'application/json',
    'tenant-id': '1',
    'Authorization': 'test1'
  }

  /**
   * 默认超时时间（毫秒）
   */
  private readonly defaultTimeout = 10000

  /**
   * GET 请求
   */
  async get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'GET',
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'POST',
      body: data,
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'PUT',
      body: data,
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'DELETE',
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'PATCH',
      body: data,
      headers: config?.headers,
      timeout: config?.timeout
    })
  }

  /**
   * 通用请求方法
   */
  private async request<T = any>(
    url: string,
    options: {
      method: string
      body?: any
      headers?: Record<string, string>
      timeout?: number
    }
  ): Promise<T> {
    try {
      const fullUrl = this.buildUrl(url)
      const headers = this.mergeHeaders(options.headers)
      const timeout = options.timeout || this.defaultTimeout

      // console.log(`[RequestService] ${options.method} ${fullUrl}`)

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(fullUrl, {
        method: options.method,
        headers,
        body: options.body ? JSON.stringify(options.body) : undefined,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text().catch(() => '无法获取错误详情')
        throw new Error(`HTTP ${response.status} ${response.statusText}: ${errorText}`)
      }

      const result = await response.json()
      // console.log(`[RequestService] ${options.method} ${fullUrl} - 成功`, result)

      // 处理响应体
      return this.processResponse(result)
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      console.error(`[RequestService] ${options.method} ${url} - 失败:`, errorMsg)
      throw error instanceof BusinessError ? error : new Error(errorMsg)
    }
  }

  /**
   * 合并请求头
   */
  private mergeHeaders(customHeaders?: Record<string, string>): Record<string, string> {
    return {
      ...this.defaultHeaders,
      ...customHeaders
    }
  }

  /**
   * 构建完整URL
   */
  private buildUrl(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }
    return `${this.baseUrl}${url.startsWith('/') ? url : `/${url}`}`
  }

  /**
   * 处理响应体
   */
  private processResponse<T = any>(
    data: any
  ): T {
    if (data && typeof data === 'object' && 'code' in data) {
      const apiResponse = data as ApiResponse<T>

      if (apiResponse.code === 0 || apiResponse.code === 200) {
        return apiResponse.data as T
      }

      const errorMessage = apiResponse.msg || apiResponse.message || '未知业务错误'

      // 特殊状态码处理
      if (apiResponse.code === 401 || apiResponse.code === 10003) {
        console.warn('[RequestService] 未授权，可能需要重新登录')
        // 在 main 进程中，可以通过 IPC 通知 renderer 进程处理登出逻辑
      }

      throw new BusinessError(apiResponse.code, errorMessage, apiResponse)
    }

    // 直接返回响应数据
    return data as T
  }
}
