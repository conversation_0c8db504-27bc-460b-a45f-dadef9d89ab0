import { useCallback } from 'react'
import { Overlay, OverlayType, StoryboardOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { byStartFrame, findStoryboardIndex } from '@/modules/video-editor/utils/overlay-helper'
import { DEFAULT_OVERLAY } from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { cloneDeep } from 'lodash'
import { TrackType } from '@/modules/video-editor/types'

export const useStoryboardHelper = () => {
  const { updateTracks } = useEditorContext()

  /**
   * 在 `base` 分镜的右方插入 `amount` 个新的分镜
   */
  const pushStoryboardAtRightOf = useCallback(
    (base: StoryboardOverlay, amount = 1) => {
      updateTracks(prevTracks => {
        const clonedTracks = cloneDeep(prevTracks)
        const totalPushedDistance = amount * DEFAULT_OVERLAY.durationInFrames
        const newId = generateNewOverlayId(prevTracks)

        for (const track of clonedTracks) {
          // 分镜所在的轨道, 需要后移后方的分镜
          if (track.type === TrackType.STORYBOARD) {
            const newStoryboards = Array
              .from({ length: amount })
              .map((_, index) => ({
                ...DEFAULT_OVERLAY,
                id: newId + index,
                type: OverlayType.STORYBOARD,
                from: base.from + base.durationInFrames + index * DEFAULT_OVERLAY.durationInFrames,
              } as Overlay))

            track.overlays.forEach(overlay => {
              if (overlay.from > base.from) {
                overlay.from += totalPushedDistance
              }
            })
            track.overlays.push(...newStoryboards)
            track.overlays.sort(byStartFrame())
          }

          // 非全局轨道, 需要后移后方的 Overlay
          if (!track.isGlobalTrack) {
            const baseStoryboardIndex = findStoryboardIndex(prevTracks, base)
            if (baseStoryboardIndex !== -1) {
              for (const overlay of track.overlays) {
                if (overlay.storyboardIndex !== undefined && overlay.storyboardIndex > baseStoryboardIndex) {
                  overlay.from += totalPushedDistance
                  overlay.storyboardIndex += amount
                }
              }
            }
          }
        }

        return clonedTracks
      })
    },
    []
  )

  /**
   * 关闭分镜下所有视频的声音
   */
  const setVolumeForVideosInStoryboard = useCallback(
    (storyboard: StoryboardOverlay, volume: number) => {
      updateTracks(prevTracks => {
        const clonedTracks = cloneDeep(prevTracks)
        const storyboardIndex = findStoryboardIndex(prevTracks, storyboard)

        for (const track of clonedTracks) {
          if (track.isGlobalTrack || track.type === TrackType.STORYBOARD) continue

          for (const overlay of track.overlays) {
            if (overlay.type === OverlayType.VIDEO && overlay.storyboardIndex === storyboardIndex) {
              overlay.styles.volume = volume
            }
          }
        }

        return clonedTracks
      })
    },
    []
  )

  /**
   * 调整分镜下所有视频的时长
   */
  const adjustDurationOfVideosInStoryboard = useCallback(
    (_storyboard: StoryboardOverlay) => {

    },
    []
  )

  /**
   * 清空分镜下所有 Overlay
   */
  const clearOverlaysInStoryboard = useCallback(
    (storyboard: StoryboardOverlay) => {
      return updateTracks(prevTracks => {
        const storyboardIndex = findStoryboardIndex(prevTracks, storyboard)

        return prevTracks.map(track => {
          if (track.type === TrackType.STORYBOARD || track.isGlobalTrack) return track

          return {
            ...track,
            overlays: track.overlays.filter(o => o.storyboardIndex !== storyboardIndex)
          }
        })
      })
    },
    []
  )

  /**
   * 保存分镜素材至素材库
   */
  const saveMaterialToLibrary = useCallback(
    (_storyboard: StoryboardOverlay) => {

    },
    []
  )

  return {
    clearOverlaysInStoryboard,
    pushStoryboardAtRightOf,
    setVolumeForVideosInStoryboard,
    adjustDurationOfVideosInStoryboard,
    saveMaterialToLibrary,
  }
}
