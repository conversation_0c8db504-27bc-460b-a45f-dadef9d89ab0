# HTTP 请求库

这是一个基于 Axios 封装的 HTTP 请求库，提供了统一的请求方法、错误处理、拦截器等功能。

## 特性

- 统一的请求方法（GET、POST、PUT、DELETE、PATCH）
- 请求和响应拦截器
- 全局错误处理
- 请求重试机制
- 支持自定义配置
- 类型安全（TypeScript）
- 支持分页请求

## 使用方法

### 基本使用

```typescript
import request from '@/libs/request';

// GET 请求
request.get('/api/users').then(data => {
  console.log(data);
});

// POST 请求
request.post('/api/users', { name: '张三', age: 25 }).then(data => {
  console.log(data);
});

// PUT 请求
request.put('/api/users/1', { name: '李四' }).then(data => {
  console.log(data);
});

// DELETE 请求
request.delete('/api/users/1').then(() => {
  console.log('删除成功');
});
```

### 带配置的请求

```typescript
request.get('/api/users', null, {
  showLoading: true,
  showErrorMessage: true,
  timeout: 5000
});

request.post('/api/users', { name: '张三' }, {
  headers: {
    'Content-Type': 'application/json',
    'X-Custom-Header': 'value'
  }
});
```

### 分页请求

```typescript
import { fetchPagination } from '@/libs/request';

// 分页请求
fetchPagination('/api/users', { page: 1, pageSize: 10 }).then(data => {
  console.log(data.list); // 列表数据
  console.log(data.total); // 总数
  console.log(data.page); // 当前页
  console.log(data.pageSize); // 每页大小
});
```

### 自定义错误处理

```typescript
request.get('/api/users', null, {
  customErrorHandler: (error) => {
    console.error('自定义错误处理:', error);
    return Promise.reject(new Error('获取用户列表失败，请稍后再试'));
  }
});
```

### 请求重试

```typescript
request.get('/api/unstable-endpoint', null, {
  retryCount: 3,
  retryDelay: 1000
});
```

## 配置

可以在 `config.ts` 文件中修改全局配置：

- `BASE_URL`: API 基础地址
- `TIMEOUT`: 请求超时时间
- `DEFAULT_HEADERS`: 默认请求头
- `HTTP_STATUS_MESSAGE`: HTTP 状态码消息映射
- `BUSINESS_CODE_MESSAGE`: 业务状态码消息映射
- `RETRY_CONFIG`: 请求重试配置
- `ENABLE_REQUEST_LOG`: 是否开启请求日志

## 自定义实例

如果需要创建自定义的 Axios 实例，可以使用 `createAxiosInstance` 函数：

```typescript
import { createAxiosInstance } from '@/libs/request';

const customInstance = createAxiosInstance({
  baseURL: 'https://api.example.com',
  timeout: 5000
});

// 使用自定义实例
customInstance.get('/users').then(data => {
  console.log(data);
});
```

## 类型定义

该库提供了完整的 TypeScript 类型定义，可以在请求时指定响应数据的类型：

```typescript
interface User {
  id: number;
  name: string;
  email: string;
}

// 指定返回类型
request.get<User>('/api/users/1').then(user => {
  console.log(user.name); // TypeScript 会提供类型提示
});
```

## 注意事项

- 默认情况下，响应会自动解析 `data` 字段，如果需要获取完整响应，请使用 `request.request` 方法
- 请求失败时，会自动显示错误提示，可以通过 `showErrorMessage: false` 关闭
- 可以通过 `showLoading: true` 在请求时显示全局加载状态 