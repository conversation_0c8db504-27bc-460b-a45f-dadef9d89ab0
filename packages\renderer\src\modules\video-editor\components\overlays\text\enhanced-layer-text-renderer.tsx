import React, { useCallback, useEffect, useRef, useState } from 'react'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import * as opentype from 'opentype.js'
import { TextToSvgConvertor } from '@clipnest/overlay-renderer'
import { continueRender, delayRender } from 'remotion'
import { useQuery } from '@tanstack/react-query'

const CURRENT_FONT_PATH_QUERY_KEYS = 'ENHANCED_CURRENT_FONT_PATH'

const PREVIEW_TEXT = '花字'
const FIXED_FONT_SIZE = 36
const FIXED_CONTAINER_HEIGHT = 80
const CONTAINER_PADDING = 8

interface EnhancedTextRendererProps {
  overlay: TextOverlay
  containerStyle: React.CSSProperties
  isPreview?: boolean
}

const TextRenderSvgPart: React.FC<{
  overlay: TextOverlay, font: opentype.Font, isPreview?: boolean
}> = ({ overlay, font, isPreview = false }) => {
  const { styles: textStyle } = overlay

  const hasStroke = Boolean(
    textStyle.strokeEnabled
    && overlay.styles.strokeWidth
    && overlay.styles.strokeColor
  )

  const hasShadow = Boolean(
    textStyle.shadowEnabled
    && overlay.styles.shadowDistance
    && overlay.styles.shadowColor
  )

  if (!hasStroke && !hasShadow) {
    return null
  }

  const svgRef = useRef<SVGSVGElement>(null)

  const generateSVGFilters = useCallback(
    () => {
      const filters: React.ReactElement[] = []

      if (hasShadow) {
        const distance = overlay.styles.shadowDistance ? overlay.styles.shadowDistance / 2 : 0
        const angle = overlay.styles.shadowAngle || 45
        const blur = overlay.styles.shadowBlur || 2
        const color = overlay.styles.shadowColor || '#000000'
        const opacity = overlay.styles.shadowOpacity || 0.5

        if (distance > 0) {
          const angleRad = (angle * Math.PI) / 180
          const offsetX = Math.cos(angleRad) * distance
          const offsetY = Math.sin(angleRad) * distance

          filters.push(
            <filter
              key="shadow"
              id={`shadow-${overlay.id}`}
              x="-100%"
              y="-100%"
              width="300%"
              height="300%"
              filterUnits="userSpaceOnUse"
              colorInterpolationFilters="sRGB"
            >
              <feDropShadow
                dx={offsetX}
                dy={offsetY}
                stdDeviation={blur}
                floodColor={color}
                floodOpacity={opacity}
              />
            </filter>
          )
        }
      }

      return filters
    },
    [hasShadow, overlay.styles, overlay.id]
  )

  const renderOpentypeSVGPath = useCallback(
    () => {
      try {
        const strokeWidth = isPreview ? 5 : overlay.styles.strokeWidth ?? 0
        const strokeColor = overlay.styles.strokeColor || '#000000'
        const filterId = hasShadow ? `shadow-${overlay.id}` : undefined

        const textToSvg = new TextToSvgConvertor(font, font)

        const centerX = 10
        const centerY = FIXED_CONTAINER_HEIGHT / 2

        const { d } = textToSvg.getD(PREVIEW_TEXT, {
          x: 0,
          y: 0,
          fontSize: FIXED_FONT_SIZE,
          anchor: 'center middle'
        })

        if (!d) return null

        // 构建变换字符串
        let transform = `translate(${centerX}, ${centerY})`

        // 添加斜体变换
        if (textStyle.fontStyle === 'italic') {
          transform += ' skewX(-12)'
        }

        const paths: React.ReactElement[] = []

        // 渲染主要文本路径
        paths.push(
          <path
            key="main-text"
            d={d}
            fill="none"
            stroke={hasStroke ? strokeColor : 'none'}
            strokeWidth={strokeWidth}
            strokeLinejoin="round"
            strokeLinecap="round"
            filter={filterId ? `url(#${filterId})` : undefined}
            transform={transform}
            style={{
              vectorEffect: 'non-scaling-stroke'
            }}
          />
        )

        // 如果是加粗，添加额外的路径
        if (textStyle.fontWeight === 'bold') {
          const boldOffset = Math.max(0.5, FIXED_FONT_SIZE * 0.01)

          const boldTransforms = [
            `translate(${centerX + boldOffset}, ${centerY})`,
            `translate(${centerX}, ${centerY + boldOffset})`,
            `translate(${centerX + boldOffset}, ${centerY + boldOffset})`
          ]

          boldTransforms.forEach((boldTransform, boldIndex) => {
            let finalBoldTransform = boldTransform
            if (textStyle.fontStyle === 'italic') {
              finalBoldTransform += ' skewX(-12)'
            }

            paths.push(
              <path
                key={`bold-${boldIndex}`}
                d={d}
                fill="none"
                stroke={hasStroke ? strokeColor : 'none'}
                strokeWidth={strokeWidth}
                strokeLinejoin="round"
                strokeLinecap="round"
                filter={filterId ? `url(#${filterId})` : undefined}
                transform={finalBoldTransform}
                style={{
                  vectorEffect: 'non-scaling-stroke'
                }}
              />
            )
          })
        }

        // 添加下划线
        if (textStyle.underlineEnabled) {
          const textWidth = font.getAdvanceWidth(PREVIEW_TEXT, FIXED_FONT_SIZE)
          const underlineY = centerY + FIXED_FONT_SIZE * 0.3
          const underlineThickness = Math.max(1, FIXED_FONT_SIZE * 0.05)

          paths.push(
            <line
              key="underline"
              x1={centerX - textWidth / 2}
              y1={underlineY}
              x2={centerX + textWidth / 2}
              y2={underlineY}
              stroke={hasStroke ? strokeColor : overlay.styles.color || '#ffffff'}
              strokeWidth={underlineThickness}
              strokeLinecap="round"
              filter={filterId ? `url(#${filterId})` : undefined}
            />
          )
        }

        return <g>{paths}</g>
      } catch (error) {
        console.error('[增强文本渲染器] SVG路径渲染失败:', error)
        return null
      }
    },
    [overlay, hasStroke, hasShadow, textStyle, isPreview, font]
  )

  const svgFilters = generateSVGFilters()
  const svgPath = renderOpentypeSVGPath()

  return (
    <svg
      ref={svgRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        pointerEvents: 'none'
      }}
      viewBox={`0 0 ${overlay.width} ${FIXED_CONTAINER_HEIGHT}`}
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        {svgFilters}
      </defs>
      {svgPath}
    </svg>
  )
}

const TextRendererCanvasPart: React.FC<{
  overlay: TextOverlay
}> = ({ overlay }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const { styles: textStyle } = overlay

  // Canvas 渲染效果（固定"花字"文本）
  useEffect(() => {
    if (!canvasRef.current || overlay.width === 0) {
      return
    }

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置 Canvas 尺寸
    canvas.width = overlay.width * window.devicePixelRatio
    canvas.height = FIXED_CONTAINER_HEIGHT * window.devicePixelRatio
    canvas.style.width = `${overlay.width}px`
    canvas.style.height = `${FIXED_CONTAINER_HEIGHT}px`

    // 缩放上下文以适应高DPI显示
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

    // 清除画布
    ctx.clearRect(0, 0, overlay.width, FIXED_CONTAINER_HEIGHT)

    // 构建字体字符串
    const fallbackFontFamily = textStyle.fontFamily || 'Arial'
    const fontString = `${FIXED_FONT_SIZE}px "${overlay.styles.fontFamily}", ${fallbackFontFamily}, sans-serif`

    // 设置文字样式
    ctx.font = fontString
    ctx.fillStyle = overlay.styles.color || '#ffffff'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    // 固定位置：居中显示
    const centerX = overlay.width / 2
    const centerY = FIXED_CONTAINER_HEIGHT / 2

    ctx.save()

    // 斜体变换
    if (textStyle.fontStyle === 'italic') {
      ctx.transform(1, 0, -0.2, 1, 0, 0)
    }

    // 渲染文本
    ctx.fillText(PREVIEW_TEXT, centerX, centerY)

    // 如果是加粗，通过多次渲染来模拟加粗效果
    if (textStyle.fontWeight === 'bold') {
      const boldOffset = Math.max(0.5, FIXED_FONT_SIZE * 0.01)

      ctx.fillText(PREVIEW_TEXT, centerX + boldOffset, centerY)
      ctx.fillText(PREVIEW_TEXT, centerX, centerY + boldOffset)
      ctx.fillText(PREVIEW_TEXT, centerX + boldOffset, centerY + boldOffset)
    }

    ctx.restore()
  }, [overlay, textStyle])

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 2,
        pointerEvents: 'none'
      }}
    />
  )
}

const OpentypeFontBasedTextRenderer: React.FC<{
  font: opentype.Font,
  overlay: TextOverlay,
  containerStyle: any,
  isPreview?: boolean
}> = ({ font, overlay, containerStyle, isPreview = false }) => {
  const { styles: textStyle } = overlay

  // 增强容器样式，确保背景图片正确显示
  const finalContainerStyle: React.CSSProperties = {
    ...containerStyle,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center'
  }

  const renderDualLayerContent = () => (
    <div style={{
      position: 'relative',
      width: '100%',
      height: FIXED_CONTAINER_HEIGHT,
      backgroundColor: textStyle.backgroundColor,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      opacity: overlay.styles.textOpacity ?? 1,
      padding: `${CONTAINER_PADDING}px`
    }}
    >
      {/* SVG 层 - 底层，负责轮廓和阴影效果 */}
      <TextRenderSvgPart overlay={overlay} font={font} isPreview={isPreview} />

      {/* Canvas 层 - 上层，负责文字填充 */}
      <TextRendererCanvasPart overlay={overlay} />
    </div>
  )

  return (
    <div
      style={{
        ...finalContainerStyle,
        position: 'relative',
        overflow: 'hidden',
        height: `${FIXED_CONTAINER_HEIGHT}px`
      }}
    >
      {renderDualLayerContent()}
    </div>
  )
}

const EnhancedTextRendererComponent: React.FC<EnhancedTextRendererProps> = ({
  overlay,
  containerStyle,
  isPreview = false
}) => {
  const [handle] = useState(() => delayRender())

  const { data: fontInfo } = useQuery({
    queryKey: [CURRENT_FONT_PATH_QUERY_KEYS, overlay.src, handle, overlay.styles.fontFamily],
    queryFn: async () => {
      const fontSrc = overlay.src
      const fontFamily = overlay.styles.fontFamily
      if (!fontSrc) {
        return null
      }

      const font = await opentype.load(overlay.src)

      try {
        const buildFontUrl = (path: string) => {
          if (path.startsWith('http://') || path.startsWith('https://')) {
            return path
          }

          const normalizedPath = path.replace(/\\/g, '/')
          if (normalizedPath.startsWith('/')) {
            return `file://${normalizedPath}`
          }

          return `file:///${normalizedPath}`
        }

        const fontUrl = buildFontUrl(fontSrc)

        const existingFontFace = Array.from(document.fonts).find(
          face => (face as FontFace).family === fontFamily
        )

        if (!existingFontFace) {
          const fontFace = new FontFace(fontFamily, `url("${fontUrl}")`)
          await fontFace.load()
          ;(document.fonts as any).add(fontFace)
          console.log(`[ENHANCED] 字体已加载到 DOM: ${fontFamily}`)
        }
      } catch (domError) {
        console.warn('[ENHANCED] DOM 字体加载失败，但 opentype.js 加载成功:', domError)
      }

      return {
        font,
        fontFamily
      }
    },
    enabled: !!overlay.src,
    staleTime: Infinity
  })

  useEffect(() => {
    (async () => {
      // 等待字体加载完成
      if (fontInfo?.fontFamily) {
        try {
          await document.fonts.ready
          // 额外检查特定字体是否已加载
          await document.fonts.load(`${overlay.styles.fontSize}px "${fontInfo.fontFamily}"`)
        } catch (error) {
          console.warn('[ENHANCED] 等待字体加载失败:', error)
        }
      }
    })()
  }, [])

  useEffect(() => {
    if (fontInfo) continueRender(handle)
  }, [fontInfo])

  if (!fontInfo) return null

  return (
    <OpentypeFontBasedTextRenderer
      font={fontInfo.font}
      overlay={overlay}
      containerStyle={containerStyle}
      isPreview={isPreview}
    />
  )
}

export const EnhancedTextRenderer = React.memo(
  EnhancedTextRendererComponent,
  (prevProps, nextProps) => {
    const basicPropsEqual = (
      prevProps.overlay.id === nextProps.overlay.id &&
      prevProps.overlay.content === nextProps.overlay.content &&
      prevProps.overlay.src === nextProps.overlay.src &&
      prevProps.overlay.width === nextProps.overlay.width &&
      prevProps.overlay.left === nextProps.overlay.left &&
      prevProps.overlay.top === nextProps.overlay.top &&
      prevProps.overlay.rotation === nextProps.overlay.rotation &&
      prevProps.isPreview === nextProps.isPreview
    )

    const stylesEqual = JSON.stringify(prevProps.overlay.styles) === JSON.stringify(nextProps.overlay.styles)

    const containerStyleEqual = JSON.stringify(prevProps.containerStyle) === JSON.stringify(nextProps.containerStyle)

    return basicPropsEqual && stylesEqual && containerStyleEqual
  }
)

