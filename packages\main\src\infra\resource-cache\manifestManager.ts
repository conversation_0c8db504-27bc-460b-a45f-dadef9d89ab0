import { promises as fsPromises } from 'fs'
import path from 'path'
import { ResourceManifest, ResourceCacheEntry } from '@app/shared/types/resource-cache.types.js'
import { app } from 'electron'

const MANIFEST_FILE_PATH = path.join(app.getPath('userData'), 'cache', 'resources', 'manifest.json')

/**
 * 加载 manifest.json 文件
 * @returns ResourceManifest 对象，如果文件不存在或解析失败则返回空对象
 */
export async function loadManifest(): Promise<ResourceManifest> {
  try {
    const data = await fsPromises.readFile(MANIFEST_FILE_PATH, 'utf-8')
    return JSON.parse(data)
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      // 文件不存在，返回空 manifest
      return {}
    }
    console.error('加载 manifest.json 失败:', error)
    return {}
  }
}

/**
 * 保存 manifest.json 文件
 * @param manifest 要保存的 ResourceManifest 对象
 */
export async function saveManifest(manifest: ResourceManifest): Promise<void> {
  try {
    await fsPromises.mkdir(path.dirname(MANIFEST_FILE_PATH), { recursive: true })
    await fsPromises.writeFile(MANIFEST_FILE_PATH, JSON.stringify(manifest, null, 2), 'utf-8')
  } catch (error) {
    console.error('保存 manifest.json 失败:', error)
  }
}

// 内存中的 manifest 实例
let currentManifest: ResourceManifest = {}

/**
 * 初始化 manifest 管理器
 * 加载 manifest 文件到内存
 */
export async function initManifestManager(): Promise<void> {
  currentManifest = await loadManifest()
}

/**
 * 获取指定 key 的缓存条目
 * @param key 资源唯一 key
 * @returns 缓存条目或 undefined
 */
export function getEntry(key: string): ResourceCacheEntry | undefined {
  return currentManifest[key]
}

/**
 * 设置（添加或更新）缓存条目
 * @param entry 要设置的缓存条目
 */
export async function setEntry(entry: ResourceCacheEntry): Promise<void> {
  currentManifest[entry.key] = entry
  await saveManifest(currentManifest)
}

/**
 * 移除指定 key 的缓存条目
 * @param key 要移除的资源唯一 key
 */
export async function removeEntry(key: string): Promise<void> {
  delete currentManifest[key]
  await saveManifest(currentManifest)
}

/**
 * 列出所有缓存条目
 * @returns 缓存条目数组
 */
export function listAllEntries(): ResourceCacheEntry[] {
  return Object.values(currentManifest)
}
