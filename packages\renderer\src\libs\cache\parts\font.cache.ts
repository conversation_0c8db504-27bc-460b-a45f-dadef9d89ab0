import localforage from 'localforage'
import { SubCacheManager } from '../types'
import opentype from 'opentype.js'
import { INDEXEDDB_DATABASE_NAME } from '@/constants/system'
import { ResourceType } from '@app/shared/types/resource-cache.types'

// 字体缓存数据结构
export interface CachedFont {
  src: string
  fontData: ArrayBuffer  // 字体二进制数据
  timestamp: number
  version?: string
  fontFamily?: string    // 字体族名称
  fontStyle?: string     // 字体样式
}

export class FontCacheManager extends SubCacheManager {

  // 字体缓存存储
  private readonly fontCacheStore: LocalForage = localforage.createInstance({
    name: INDEXEDDB_DATABASE_NAME,
    storeName: 'font_cache',
    description: '字体缓存存储'
  })

  // 内存缓存，用于同步访问 opentype.Font 对象
  private memoryCache: Map<string, opentype.Font> = new Map()

  /**
   * 缓存字体文件并解析为 opentype.Font 对象
   * @param src 字体文件路径或URL
   * @param version 字体版本（可选）
   */
  public async cacheFont(src: string, version?: string): Promise<opentype.Font> {
    const existed = this.memoryCache.get(src)

    // 检查内存缓存
    if (existed) return existed

    // 检查 IndexedDB 缓存
    const cachedFont = await this.getCachedFontFromDB(src)
    if (cachedFont) {
      try {
        // 从缓存的二进制数据恢复字体对象
        const font = opentype.parse(cachedFont.fontData)
        this.memoryCache.set(src, font)
        console.debug(`[font.cache] 从 IndexedDB 恢复字体: ${cachedFont.fontFamily || '未知字体'}`)
        return font
      } catch (error) {
        console.warn(`[font.cache] 恢复字体失败，将重新加载: ${src}`, error)
        // 清理损坏的缓存项
        await this.fontCacheStore.removeItem(src)
      }
    }

    let font: opentype.Font
    let fontData: ArrayBuffer

    // 检查是否是本地字体文件（public 目录中的字体）
    if (this.isLocalFont(src)) {
      font = await opentype.load(src)
      // 获取字体二进制数据
      const response = await fetch(src)
      fontData = await response.arrayBuffer()
    } else {
      // 处理远程字体文件 - 通过 IPC 获取本地缓存路径
      const cachedLocalUrl = await window.resource.fetchOrSaveResource({
        url: src,
        type: ResourceType.FONT,
      })

      if (!cachedLocalUrl) {
        throw new Error(`无法获取字体文件: ${src}`)
      }

      // 读取字体二进制数据
      const response = await fetch(`file://${cachedLocalUrl}`)
      fontData = await response.arrayBuffer()
      font = opentype.parse(fontData)
    }

    // 创建缓存条目
    const cacheEntry: CachedFont = {
      src,
      fontData,
      timestamp: Date.now(),
      version,
      fontFamily: font.names.fontFamily?.zh || font.names.fontFamily?.en || '未知字体',
      fontStyle: font.names.fontSubfamily?.zh || font.names.fontSubfamily?.en
    }

    // 双写：同时保存到 IndexedDB 和内存缓存
    await this.fontCacheStore.setItem(src, cacheEntry)
    this.memoryCache.set(src, font)

    console.debug(`[font.cache] 已将字体 ${cacheEntry.fontFamily} 缓存到内存和 IndexedDB`)
    return font
  }

  /**
   * 同步获取字体对象（从内存缓存）
   */
  public getFont(src: string): opentype.Font | null {
    return this.memoryCache.get(src) || null
  }

  public getLocalUrl(src: string) {
    return window.resource.getResourcePath({ url: src, type: ResourceType.FONT })
  }

  /**
   * 从 IndexedDB 获取缓存的字体数据
   */
  private async getCachedFontFromDB(src: string): Promise<CachedFont | null> {
    try {
      return await this.fontCacheStore.getItem<CachedFont>(src)
    } catch (error) {
      console.error(`[font.cache] 获取缓存字体失败: ${src}`, error)
      return null
    }
  }

  /**
   * 判断是否是本地字体文件（public 目录中的字体）
   * @param src 字体文件路径
   * @returns 是否是本地字体
   */
  private isLocalFont(src: string): boolean {
    // 检查是否是以 /fonts/ 或 ./fonts/ 开头的本地字体路径
    return src.startsWith('/fonts/') || src.startsWith('./fonts/')
  }

  /**
   * 初始化方法：从 IndexedDB 恢复字体到内存缓存
   */
  override init(): void {
    void this.fontCacheStore
      .iterate(async (cachedFont: CachedFont, src: string) => {
        try {
          // 从缓存的二进制数据恢复字体对象
          const font = opentype.parse(cachedFont.fontData)
          this.memoryCache.set(src, font)
          console.debug(`[font.cache] 初始化恢复字体: ${cachedFont.fontFamily || src}`)
        } catch (error) {
          console.warn(`[font.cache] 初始化时恢复字体失败，将清理损坏的缓存: ${src}`, error)
          // 清理损坏的缓存项
          await this.fontCacheStore.removeItem(src)
        }
      })
      .catch(error => {
        console.error('[font.cache] 初始化字体缓存失败:', error)
      })
  }

  /**
   * 清理过期的字体缓存
   */
  override async cleanup(now: number, maxAge: number): Promise<void> {
    const keysToRemove: string[] = []

    try {
      // 遍历 IndexedDB 中的字体缓存
      await this.fontCacheStore.iterate((cachedFont: CachedFont, src: string) => {
        if (now - cachedFont.timestamp > maxAge) {
          keysToRemove.push(src)
        }
      })

      // 清理过期的缓存项
      for (const src of keysToRemove) {
        await this.fontCacheStore.removeItem(src)
        this.memoryCache.delete(src)
        console.debug(`[font.cache] 清理过期字体缓存: ${src}`)
      }

      if (keysToRemove.length > 0) {
        console.debug(`[font.cache] 清理了 ${keysToRemove.length} 个过期字体缓存`)
      }
    } catch (error) {
      console.error('[font.cache] 清理字体缓存失败:', error)
    }
  }
}
