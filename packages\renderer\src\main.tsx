import React, { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ErrorHandlerProvider } from '@/contexts'
import { ModalProvider } from './libs/tools/modal'
// 初始化缓存系统
import '@/libs/cache/cache-initializer'

// 设置默认暗色主题
const savedTheme = localStorage.getItem('theme')

const isDark = savedTheme ? savedTheme === 'dark' : true

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchInterval: false,
      staleTime: Infinity

    },
  },
})

if (isDark) {
  document.documentElement.classList.add('dark')
} else {
  document.documentElement.classList.remove('dark')
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <ModalProvider>
        <ErrorHandlerProvider>
          <App />
        </ErrorHandlerProvider>
      </ModalProvider>
    </QueryClientProvider>
  </StrictMode>,
)
