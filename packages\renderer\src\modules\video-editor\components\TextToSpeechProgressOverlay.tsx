import React from 'react'
import { CircularProgress } from '@/components/ui/circular-progress'
import { cn } from '@/components/lib/utils'

/**
 * 文本转语音任务状态
 */
export interface TTSTaskStatus {
  taskId: string
  text: string
  isPending: boolean
  audioUrl?: string
  error?: string
}

/**
 * 文本转语音进度状态
 */
export interface TTSProgressState {
  visible: boolean
  completed: number
  total: number
  tasks: TTSTaskStatus[]
}

interface TextToSpeechProgressOverlayProps {
  progressState: TTSProgressState
}

/**
 * 文本转语音进度遮罩组件
 */
export function TextToSpeechProgressOverlay({ progressState }: TextToSpeechProgressOverlayProps) {
  const { visible, completed, total } = progressState

  if (!visible) return null

  const progress = total > 0 ? (completed / total) * 100 : 0

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm',
      )}
    >
      <div className="bg-background rounded-lg p-8 shadow-lg border max-w-sm w-full mx-4">
        <div className="flex flex-col items-center space-y-6">
          {/* 圆环进度条 */}
          <CircularProgress
            value={progress}
            size={120}
            strokeWidth={8}
            showPercentage={false}
            color="oklch(0.715 0.143 215.221)"
            backgroundColor="oklch(0.551 0.027 264.364)"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {completed}
              </div>
              <div className="text-sm text-muted-foreground">
                / {total}
              </div>
            </div>
          </CircularProgress>

          {/* 状态文本 */}
          <div className="text-center">
            <h3 className="text-lg font-medium text-foreground">
              正在生成语音
            </h3>
          </div>
        </div>
      </div>
    </div>
  )
}
