import React, { useCallback } from 'react'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON><PERSON>oot<PERSON>, ModalHeader } from '@/components/modal'
import { Button } from '@/components/ui/button'
import { ChevronRight } from 'lucide-react'
import { genForm } from '@/libs/tools/form'
import { useModal } from '@/libs/tools/modal'

const DownloadForm = genForm(
  z.object({
    path: z.string().min(1, '请选择下载路径'),
  }),
  {
    fields: {
      path: {
        label: '下载路径',
        render: ({ field }) => {
          // const { setValue } = useFormContext()

          return (
            <div className="relative">
              <Input {...field} placeholder="请选择下载路径" className="pr-8" />
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-1/2 -translate-y-1/2 right-1 size-6"
                onClick={async () => {
                  // const [path] = await window.fileUploader.selectFolder({
                  //   title: '选择下载路径',
                  //   multiple: false,
                  // })
                  // setValue('path', path)
                }}
              >
                <ChevronRight className="size-4" />
              </Button>
            </div>
          )
        },
      },
    },
  },
)

function DownloadModal({ urls }: { urls: string[] }) {
  return (
    <>
      <ModalHeader title="下载至" description={`本次将下载 ${urls.length} 个视频`} />
      <DownloadForm
        defaultValues={{ path: '' }}
        onSubmit={async data => {
          console.log(data)
        }}
      >
        <ModalFooter />
      </DownloadForm>
    </>
  )
}

export function useDownload() {
  const modal = useModal()

  return useCallback(
    (urls: string[]) =>
      modal({
        content: <DownloadModal urls={urls} />,
      }),
    [],
  )
}
